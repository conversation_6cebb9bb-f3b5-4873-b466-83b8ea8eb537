package com.siemens.spm.analysis.detectorreport.template.aggregation;

import com.siemens.spm.analysis.domain.DetectorTemplate;
import com.siemens.spm.analysis.strategy.DetectorReportStrategy;
import com.siemens.spm.analysis.vo.detectorreport.DetectorReportChartVO;
import com.siemens.spm.usermanagementservice.api.vo.IntersectionInternalVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Strategy for weekly aggregation of detector reports.
 * Processes data in weekly intervals for days specified in the template.
 *
 * <AUTHOR>
 * @version 1.0
 * @since 01/07/2025
 */
@Component
@Slf4j
public class WeeklyAggregationStrategy extends BaseAggregationStrategy {

    private static final int DAY_INTERVAL = 1;

    public WeeklyAggregationStrategy(DetectorReportStrategy detectorReportStrategy) {
        super(detectorReportStrategy);
    }

    @Override
    public List<DetectorReportChartVO> aggregatePeriods(
            DetectorTemplate template,
            IntersectionInternalVO intersection,
            List<LocalDateTime> targetDateTimes) {

        List<DetectorReportChartVO> resultCharts = createEmptyResultList();

        List<Long> filterDetectorIds = getFilterDetectorIds(template.getDetectorsJson());
        Integer agencyId = template.getAgencyId();
        String intersectionId = intersection.getId();

        // Group target date times by week and process each week
        for (LocalDateTime targetDateTime : targetDateTimes) {
            LocalDate targetDate = targetDateTime.toLocalDate();

            // Find the start of the week (Monday) for this target date
            LocalDate weekStart = targetDate.with(DayOfWeek.MONDAY);
            LocalDate weekEnd = calculateWeekEndDate(weekStart, targetDate);

            log.info("Processing weekly data for target date: {}, week range: {} to {}", targetDate, weekStart,
                    weekEnd);

            // Process the current week and add result to charts if data exists
            processWeek(template, weekStart, weekEnd, filterDetectorIds, agencyId, intersectionId)
                    .ifPresent(resultCharts::add);

            log.debug("End analysis weekly detector report for target date: {}", targetDate);
        }

        return resultCharts;
    }

    /**
     * Calculates the end date for the current week period.
     * End date is either Sunday of current week or targetDate if it comes earlier.
     */
    private LocalDate calculateWeekEndDate(LocalDate weekStart, LocalDate targetDate) {
        LocalDate weekEnd = weekStart.with(DayOfWeek.SUNDAY);
        return weekEnd.isAfter(targetDate) ? targetDate : weekEnd;
    }

    /**
     * Processes a single week period, collecting and aggregating daily reports.
     *
     * @return Optional containing the aggregated report if data exists, empty otherwise
     */
    private Optional<DetectorReportChartVO> processWeek(
            DetectorTemplate template,
            LocalDate weekStart,
            LocalDate weekEnd,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        List<DetectorReportChartVO> weeklyReports = collectDailyReportsForWeek(
                template, weekStart, weekEnd, filterDetectorIds, agencyId, intersectionId);

        if (weeklyReports.isEmpty()) {
            log.debug("No data found for calendar week {} to {} after applying week days filter",
                    weekStart, weekEnd);
            return Optional.empty();
        }

        log.debug("Creating aggregated report for calendar week {} to {}", weekStart, weekEnd);
        DetectorReportChartVO aggregatedReportPerWeek = combinePeriodReports(weeklyReports);

        // Calculate weekly metrics and set time boundaries
        calculateWeeklyMetrics(aggregatedReportPerWeek);
        setReportTimeBoundaries(aggregatedReportPerWeek, weekStart, weekEnd);

        log.debug("Added weekly report for period {} to {} with {} days of data",
                weekStart, weekEnd, weeklyReports.size());

        return Optional.of(aggregatedReportPerWeek);
    }

    /**
     * Collects daily detector reports for each applicable day in the week.
     */
    private List<DetectorReportChartVO> collectDailyReportsForWeek(
            DetectorTemplate template,
            LocalDate weekStart,
            LocalDate weekEnd,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        List<DetectorReportChartVO> dailyReports = new ArrayList<>();
        LocalDate currentDate = weekStart;

        while (!currentDate.isAfter(weekEnd)) {
            collectDailyReport(template, currentDate, filterDetectorIds, agencyId, intersectionId)
                    .ifPresent(dailyReports::add);
            currentDate = currentDate.plusDays(DAY_INTERVAL);
        }

        return dailyReports;
    }

    /**
     * Collects detector report data for a single day.
     */
    private Optional<DetectorReportChartVO> collectDailyReport(
            DetectorTemplate template,
            LocalDate date,
            List<Long> filterDetectorIds,
            Integer agencyId,
            String intersectionId) {

        LocalDateTime fromTime = template.getStartTime().atDate(date);
        LocalDateTime toTime = template.getEndTime().atDate(date);

        log.debug("Analyzing detector data for {} from {} to {}", date.getDayOfWeek(), fromTime, toTime);
        return detectorReportAnalysis(agencyId, intersectionId, fromTime, toTime, filterDetectorIds);
    }

    /**
     * Calculates weekly metrics for all detectors and phases in the report.
     */
    private void calculateWeeklyMetrics(DetectorReportChartVO report) {
        report.getDetectorDataMap().forEach((detectorId, detectorData) ->
                detectorData.getDetectorPhaseDataMap().forEach((phaseId, phaseData) ->
                        phaseData.calculateReportWeekly()));
    }

    /**
     * Sets the time boundaries for the aggregated report.
     */
    private void setReportTimeBoundaries(
            DetectorReportChartVO report,
            LocalDate weekStart,
            LocalDate weekEnd) {
        report.setFromTime(weekStart.atStartOfDay());
        report.setToTime(weekEnd.plusDays(1).atStartOfDay());
    }

}
